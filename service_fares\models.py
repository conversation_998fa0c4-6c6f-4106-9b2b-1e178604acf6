from django.db import models
from RTRDA.model import BaseModel


class Servicefares(BaseModel):
    routeid = models.IntegerField(db_column='RouteId')
    originstation = models.CharField(db_column='OriginStation', max_length=500, db_collation='Thai_CI_AI')
    destinationstation = models.CharField(db_column='DestinationStation', max_length=500, db_collation='Thai_CI_AI')
    faretype = models.CharField(db_column='FareType', max_length=1, db_collation='Thai_CI_AI')
    fareamount = models.FloatField(db_column='FareAmount')
    servicetype = models.CharField(db_column='ServiceType', max_length=1, db_collation='Thai_CI_AI')
    frequency = models.CharField(db_column='Frequency', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    operationstart = models.Char<PERSON><PERSON>(db_column='OperationStart', max_length=10, db_collation='Thai_CI_AI', blank=True, null=True)
    operationend = models.CharField(db_column='OperationEnd', max_length=10, db_collation='Thai_CI_AI', blank=True, null=True)
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'ServiceFares'

